<template>
  <div class="essay-requirements-page">
    <!-- 状态栏 -->
    <div class="status-bar">
      <div class="status-left">
        <div class="signal-bars">
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
          <div class="bar"></div>
        </div>
      </div>
      <div class="status-right">
        <div class="battery-indicator">
          <div class="battery-level"></div>
        </div>
      </div>
    </div>

    <!-- 头部导航 -->
    <div class="header">
      <button class="back-btn" @click="goBack">
        <img :src="arrowLeft" alt="返回" />
      </button>
      <h1 class="page-title">作文要求</h1>
      <div class="header-placeholder"></div>
    </div>

    <!-- 作文信息 -->
    <div class="essay-info">
      <div class="essay-tags">
        <span class="unit-tag">第三单元</span>
        <span class="type-tag">单元作文</span>
        <span class="category-tag">全命题</span>
      </div>
      <h2 class="essay-title">我的植物朋友</h2>
      <div class="essay-time">截止时间：2025.10.15 23:59</div>
    </div>

    <!-- 作文要求内容 -->
    <div class="requirements-content">
      <div class="requirement-section">
        <h3 class="section-title">写作要求</h3>
        <div class="requirement-text">
          <p>1. 选择一种你熟悉的植物，仔细观察它的外形特点。</p>
          <p>2. 描述这种植物的颜色、形状、大小等特征。</p>
          <p>3. 写出你和这种植物之间发生的有趣故事。</p>
          <p>4. 表达你对这种植物的喜爱之情。</p>
          <p>5. 字数要求：不少于200字。</p>
        </div>
      </div>

      <div class="requirement-section">
        <h3 class="section-title">评分标准</h3>
        <div class="scoring-criteria">
          <div class="criteria-item">
            <span class="criteria-label">内容完整性</span>
            <span class="criteria-score">30分</span>
          </div>
          <div class="criteria-item">
            <span class="criteria-label">语言表达</span>
            <span class="criteria-score">25分</span>
          </div>
          <div class="criteria-item">
            <span class="criteria-label">结构层次</span>
            <span class="criteria-score">20分</span>
          </div>
          <div class="criteria-item">
            <span class="criteria-label">创意想象</span>
            <span class="criteria-score">15分</span>
          </div>
          <div class="criteria-item">
            <span class="criteria-label">书写规范</span>
            <span class="criteria-score">10分</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 上传区域 -->
    <div class="upload-section">
      <div class="upload-area" @click="selectFile" :class="{ 'has-file': selectedFile }">
        <div v-if="!selectedFile" class="upload-placeholder">
          <img :src="uploadIcon" alt="上传" class="upload-icon" />
          <div class="upload-text">点击上传作文图片</div>
          <div class="upload-hint">支持 JPG、PNG 格式</div>
        </div>
        <div v-else class="file-preview">
          <img :src="filePreview" alt="预览" class="preview-image" />
          <div class="file-info">
            <div class="file-name">{{ selectedFile.name }}</div>
            <button class="remove-file" @click.stop="removeFile">删除</button>
          </div>
        </div>
      </div>
      <input 
        ref="fileInput" 
        type="file" 
        accept="image/*" 
        @change="handleFileSelect" 
        style="display: none;"
      />
    </div>

    <!-- 提交按钮 -->
    <div class="submit-section">
      <button 
        class="submit-btn" 
        :disabled="!selectedFile" 
        @click="submitEssay"
        :class="{ 'disabled': !selectedFile }"
      >
        提交作文
      </button>
    </div>
  </div>
</template>

<script>
import arrowLeft from '../assets/images/arrow-left.svg'
import uploadIcon from '../assets/images/upload-icon.svg'

export default {
  name: 'EssayRequirementsPage',
  emits: ['back'],
  data() {
    return {
      arrowLeft,
      uploadIcon,
      selectedFile: null,
      filePreview: null
    }
  },
  methods: {
    goBack() {
      this.$emit('back')
    },
    selectFile() {
      this.$refs.fileInput.click()
    },
    handleFileSelect(event) {
      const file = event.target.files[0]
      if (file) {
        this.selectedFile = file
        
        // 创建预览
        const reader = new FileReader()
        reader.onload = (e) => {
          this.filePreview = e.target.result
        }
        reader.readAsDataURL(file)
      }
    },
    removeFile() {
      this.selectedFile = null
      this.filePreview = null
      this.$refs.fileInput.value = ''
    },
    submitEssay() {
      if (!this.selectedFile) {
        return
      }
      
      console.log('提交作文:', this.selectedFile.name)
      // 这里可以添加实际的上传逻辑
      alert('作文提交成功！')
      this.goBack()
    }
  }
}
</script>

<style scoped>
.essay-requirements-page {
  width: 375px;
  height: 844px;
  background: #FFFFFF;
  position: relative;
  margin: 0 auto;
  box-shadow: 0px 3px 6px 0px rgba(18, 15, 40, 0.12);
  font-family: 'Inter', sans-serif;
  overflow-y: auto;
}

/* 状态栏 */
.status-bar {
  width: 100%;
  height: 40px;
  background: transparent;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 12px;
  box-sizing: border-box;
}

.status-left {
  display: flex;
  align-items: center;
}

.signal-bars {
  display: flex;
  gap: 2px;
  align-items: flex-end;
}

.bar {
  width: 2.55px;
  background: #000000;
}

.bar:nth-child(1) { height: 3.4px; }
.bar:nth-child(2) { height: 5.53px; }
.bar:nth-child(3) { height: 8.51px; }
.bar:nth-child(4) { height: 10.21px; }

.status-right {
  display: flex;
  align-items: center;
}

.battery-indicator {
  width: 20.28px;
  height: 10.06px;
  border: 1px solid #000000;
  border-radius: 2px;
  position: relative;
  opacity: 0.35;
}

.battery-level {
  width: 17.87px;
  height: 7.66px;
  background: #000000;
  position: absolute;
  top: 1.2px;
  left: 1.21px;
}

/* 头部导航 */
.header {
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 16px;
  border-bottom: 1px solid #F3F4F6;
}

.back-btn {
  background: none;
  border: none;
  padding: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.back-btn img {
  width: 24px;
  height: 24px;
}

.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #171A1F;
  margin: 0;
}

.header-placeholder {
  width: 40px;
}

/* 作文信息 */
.essay-info {
  padding: 20px 24px;
  border-bottom: 1px solid #F3F4F6;
}

.essay-tags {
  display: flex;
  gap: 12px;
  margin-bottom: 12px;
}

.unit-tag, .type-tag, .category-tag {
  font-size: 12px;
  line-height: 18px;
  color: #636AE8;
  background: #F0F1FF;
  padding: 2px 8px;
  border-radius: 4px;
}

.essay-title {
  font-size: 20px;
  font-weight: 600;
  color: #171A1F;
  margin: 0 0 8px 0;
}

.essay-time {
  font-size: 14px;
  color: #DE3B40;
}

/* 要求内容 */
.requirements-content {
  padding: 0 24px;
}

.requirement-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: #171A1F;
  margin: 0 0 12px 0;
}

.requirement-text p {
  font-size: 14px;
  line-height: 22px;
  color: #323842;
  margin: 0 0 8px 0;
}

.scoring-criteria {
  background: #F8F9FA;
  border-radius: 8px;
  padding: 16px;
}

.criteria-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #E5E7EB;
}

.criteria-item:last-child {
  border-bottom: none;
}

.criteria-label {
  font-size: 14px;
  color: #323842;
}

.criteria-score {
  font-size: 14px;
  font-weight: 600;
  color: #636AE8;
}

/* 上传区域 */
.upload-section {
  padding: 24px;
}

.upload-area {
  border: 2px dashed #D1D5DB;
  border-radius: 8px;
  padding: 32px 16px;
  text-align: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.upload-area:hover {
  border-color: #636AE8;
  background: #F9FAFB;
}

.upload-area.has-file {
  border-style: solid;
  border-color: #636AE8;
  background: #F0F1FF;
}

.upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.upload-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 12px;
}

.upload-text {
  font-size: 16px;
  font-weight: 500;
  color: #323842;
  margin-bottom: 4px;
}

.upload-hint {
  font-size: 12px;
  color: #9095A0;
}

.file-preview {
  display: flex;
  align-items: center;
  gap: 12px;
}

.preview-image {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.file-info {
  flex: 1;
  text-align: left;
}

.file-name {
  font-size: 14px;
  color: #323842;
  margin-bottom: 8px;
}

.remove-file {
  background: #DE3B40;
  color: white;
  border: none;
  padding: 4px 12px;
  border-radius: 4px;
  font-size: 12px;
  cursor: pointer;
}

/* 提交按钮 */
.submit-section {
  padding: 0 24px 32px;
}

.submit-btn {
  width: 100%;
  height: 48px;
  background: #636AE8;
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: background 0.3s ease;
}

.submit-btn:hover:not(.disabled) {
  background: #5258d6;
}

.submit-btn.disabled {
  background: #D1D5DB;
  cursor: not-allowed;
}
</style>
